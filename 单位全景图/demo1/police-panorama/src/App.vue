<script setup lang="ts">
import PanoramaVisualization from './components/PanoramaVisualization.vue'
</script>

<template>
  <div class="app-container">
    <!-- 标题栏 -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">🚔</div>
          <div class="title-section">
            <h1 class="main-title glow-text">公安内部单位全景图</h1>
            <p class="subtitle">Police Unit Panoramic Visualization System</p>
          </div>
        </div>
        <div class="header-info">
          <div class="current-time">{{ currentTime }}</div>
          <div class="system-status">
            <span class="status-dot"></span>
            系统正常运行
          </div>
        </div>
      </div>
    </header>

    <!-- 主要可视化区域 -->
    <main class="main-content">
      <PanoramaVisualization />
    </main>

    <!-- 底部信息栏 -->
    <footer class="app-footer">
      <div class="footer-content">
        <div class="unit-info">
          <span class="current-unit">当前单位：保定市第一中心医院</span>
          <span class="last-update">最后更新：{{ lastUpdate }}</span>
        </div>
        <div class="operation-tips">
          <span class="tip">💡 点击节点查看详细信息</span>
          <span class="tip">🖱️ 鼠标悬停查看连线效果</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentTime = ref('')
const lastUpdate = ref('')

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const updateLastUpdate = () => {
  const now = new Date()
  lastUpdate.value = now.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

let timeInterval: number

onMounted(() => {
  updateTime()
  updateLastUpdate()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--dark-blue) 0%, var(--primary-blue) 50%, var(--dark-blue) 100%);
  overflow: hidden;
}

.app-header {
  height: 80px;
  background: linear-gradient(90deg, rgba(0, 51, 102, 0.9), rgba(0, 17, 34, 0.9));
  border-bottom: 2px solid var(--tech-blue);
  backdrop-filter: blur(10px);
  z-index: 100;
}

.header-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  font-size: 2.5rem;
  animation: float 3s ease-in-out infinite;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.main-title {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--white);
  margin: 0;
  text-shadow: 0 0 10px var(--tech-blue);
}

.subtitle {
  font-size: 0.9rem;
  color: var(--gray);
  margin: 0;
  font-style: italic;
}

.header-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.current-time {
  font-size: 1.1rem;
  color: var(--tech-blue);
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--success-green);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--success-green);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.main-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.app-footer {
  height: 60px;
  background: linear-gradient(90deg, rgba(0, 17, 34, 0.9), rgba(0, 51, 102, 0.9));
  border-top: 1px solid var(--tech-blue);
  backdrop-filter: blur(10px);
  z-index: 100;
}

.footer-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
}

.unit-info {
  display: flex;
  gap: 30px;
  color: var(--white);
  font-size: 0.9rem;
}

.current-unit {
  color: var(--tech-blue);
  font-weight: bold;
}

.last-update {
  color: var(--gray);
}

.operation-tips {
  display: flex;
  gap: 20px;
}

.tip {
  color: var(--gray);
  font-size: 0.8rem;
  opacity: 0.8;
}

@media (max-width: 1024px) {
  .app-header {
    height: 70px;
  }

  .main-title {
    font-size: 1.6rem;
  }

  .subtitle {
    font-size: 0.85rem;
  }

  .current-time {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .app-header {
    height: 60px;
  }

  .header-content {
    padding: 0 15px;
    flex-direction: column;
    gap: 10px;
  }

  .logo-section {
    gap: 10px;
  }

  .logo-icon {
    font-size: 2rem;
  }

  .main-title {
    font-size: 1.2rem;
  }

  .subtitle {
    font-size: 0.7rem;
  }

  .header-info {
    flex-direction: row;
    gap: 15px;
  }

  .current-time {
    font-size: 0.9rem;
  }

  .system-status {
    font-size: 0.8rem;
  }

  .app-footer {
    height: 50px;
  }

  .footer-content {
    padding: 0 15px;
    flex-direction: column;
    gap: 8px;
  }

  .unit-info {
    gap: 15px;
    font-size: 0.8rem;
  }

  .operation-tips {
    flex-direction: column;
    gap: 3px;
    text-align: center;
  }

  .tip {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    height: 50px;
  }

  .header-content {
    padding: 0 10px;
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .main-title {
    font-size: 1rem;
  }

  .subtitle {
    display: none;
  }

  .current-time {
    font-size: 0.8rem;
  }

  .system-status {
    font-size: 0.7rem;
  }

  .app-footer {
    height: 40px;
  }

  .footer-content {
    padding: 0 10px;
  }

  .unit-info {
    gap: 10px;
    font-size: 0.7rem;
  }

  .operation-tips {
    display: none;
  }
}
</style>
