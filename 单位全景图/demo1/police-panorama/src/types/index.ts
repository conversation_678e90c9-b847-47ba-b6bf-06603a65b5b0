// 基础信息接口
export interface BasicInfo {
  unitName: string;
  district: string;
  address: string;
  policeStation: string;
  priorityLevel: string;
}

// 三防模块接口
export interface SecurityModule {
  monitorCount: number;
  securityPersonnel: number;
}

// 专职保卫接口
export interface SecurityGuard {
  name: string;
  phone: string;
}

// 案件信息接口
export interface CaseInfo {
  thisYear: {
    criminal: number;
    administrative: number;
    theft: number;
    robbery: number;
  };
  lastYear: {
    criminal: number;
    administrative: number;
    theft: number;
    robbery: number;
  };
}

// 行业信息接口（以医院为例）
export interface IndustryInfo {
  type: 'hospital' | 'school' | 'bank' | 'other';
  data: {
    [key: string]: number | string;
  };
}

// 背审情况接口
export interface BackgroundCheck {
  completedCount: number;
  noRiskCount: number;
}

// 工作绩效接口
export interface WorkPerformance {
  policeOfficers: Array<{
    name: string;
    position: string;
  }>;
  inspectionCompletion: {
    frequency: string;
    completionRate: number;
  };
}

// 风险评估接口
export interface RiskAssessment {
  score: number;
  level: 'low' | 'medium' | 'high';
  issues: string[];
}

// 节点位置接口
export interface NodePosition {
  x: number;
  y: number;
  z?: number;
}

// 数据模块接口
export interface DataModule {
  id: string;
  name: string;
  type: 'basic' | 'security' | 'guard' | 'case' | 'industry' | 'background' | 'performance' | 'risk';
  position: NodePosition;
  data: any;
  color: string;
}

// 主要数据接口
export interface UnitData {
  basicInfo: BasicInfo;
  securityModule: SecurityModule;
  securityGuard: SecurityGuard;
  caseInfo: CaseInfo;
  industryInfo: IndustryInfo;
  backgroundCheck: BackgroundCheck;
  workPerformance: WorkPerformance;
  riskAssessment: RiskAssessment;
}

// 连线接口
export interface Connection {
  from: string;
  to: string;
  color: string;
  animated: boolean;
}

// 弹窗数据接口
export interface ModalData {
  title: string;
  content: any;
  position: { x: number; y: number };
  visible: boolean;
}
