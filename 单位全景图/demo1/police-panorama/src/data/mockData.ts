import type { UnitData, DataModule } from '@/types';

// 保定市第一中心医院示例数据
export const mockUnitData: UnitData = {
  basicInfo: {
    unitName: '保定市第一中心医院',
    district: '莲池区',
    address: '保定市莲池区长城北大街320号',
    policeStation: '五四路派出所',
    priorityLevel: '重点单位'
  },
  securityModule: {
    monitorCount: 156,
    securityPersonnel: 24
  },
  securityGuard: {
    name: '张建国',
    phone: '13812345678'
  },
  caseInfo: {
    thisYear: {
      criminal: 2,
      administrative: 8,
      theft: 1,
      robbery: 0
    },
    lastYear: {
      criminal: 3,
      administrative: 12,
      theft: 2,
      robbery: 1
    }
  },
  industryInfo: {
    type: 'hospital',
    data: {
      bedCount: 1200,
      doctorCount: 380,
      nurseCount: 520,
      dailyPatients: 2800,
      emergencyDept: '24小时',
      specialDepts: 28
    }
  },
  backgroundCheck: {
    completedCount: 900,
    noRiskCount: 895
  },
  workPerformance: {
    policeOfficers: [
      { name: '李明', position: '社区民警' },
      { name: '王强', position: '片区警长' }
    ],
    inspectionCompletion: {
      frequency: '每月2次',
      completionRate: 95
    }
  },
  riskAssessment: {
    score: 85,
    level: 'medium',
    issues: [
      '人员流动性大',
      '夜间安保力量相对薄弱',
      '停车场监控盲区需要补充'
    ]
  }
};

// 数据模块配置
export const dataModules: DataModule[] = [
  {
    id: 'basic',
    name: '基本信息',
    type: 'basic',
    position: { x: 0, y: 0 },
    data: mockUnitData.basicInfo,
    color: '#00D4FF'
  },
  {
    id: 'security',
    name: '三防模块',
    type: 'security',
    position: { x: 300, y: -200 },
    data: mockUnitData.securityModule,
    color: '#FF6B6B'
  },
  {
    id: 'guard',
    name: '专职保卫',
    type: 'guard',
    position: { x: 300, y: 200 },
    data: mockUnitData.securityGuard,
    color: '#4ECDC4'
  },
  {
    id: 'case',
    name: '案件信息',
    type: 'case',
    position: { x: -300, y: -200 },
    data: mockUnitData.caseInfo,
    color: '#FFE66D'
  },
  {
    id: 'industry',
    name: '行业信息',
    type: 'industry',
    position: { x: -300, y: 200 },
    data: mockUnitData.industryInfo,
    color: '#A8E6CF'
  },
  {
    id: 'background',
    name: '背审情况',
    type: 'background',
    position: { x: 0, y: -300 },
    data: mockUnitData.backgroundCheck,
    color: '#FFB3BA'
  },
  {
    id: 'performance',
    name: '工作绩效',
    type: 'performance',
    position: { x: 0, y: 300 },
    data: mockUnitData.workPerformance,
    color: '#BFEFFF'
  },
  {
    id: 'risk',
    name: '风险评估',
    type: 'risk',
    position: { x: 400, y: 0 },
    data: mockUnitData.riskAssessment,
    color: '#FFDFBA'
  }
];

// 连线配置
export const connections = [
  { from: 'basic', to: 'security', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'guard', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'case', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'industry', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'background', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'performance', color: '#00D4FF', animated: true },
  { from: 'basic', to: 'risk', color: '#00D4FF', animated: true }
];
