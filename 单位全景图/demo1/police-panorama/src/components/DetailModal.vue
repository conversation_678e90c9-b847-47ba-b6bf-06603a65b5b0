<template>
  <div class="modal-overlay" @click="handleOverlayClick">
    <div 
      class="modal-container"
      :style="modalStyle"
      @click.stop
    >
      <div class="modal-header">
        <h3 class="modal-title glow-text">{{ data.title }}</h3>
        <button class="close-btn" @click="$emit('close')">
          <span>×</span>
        </button>
      </div>
      
      <div class="modal-content">
        <!-- 基本信息 -->
        <div v-if="isBasicInfo" class="info-section">
          <div class="info-item">
            <span class="label">单位名称：</span>
            <span class="value">{{ data.content.unitName }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属区县：</span>
            <span class="value">{{ data.content.district }}</span>
          </div>
          <div class="info-item">
            <span class="label">详细地址：</span>
            <span class="value">{{ data.content.address }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属派出所：</span>
            <span class="value">{{ data.content.policeStation }}</span>
          </div>
          <div class="info-item">
            <span class="label">重点等级：</span>
            <span class="value priority">{{ data.content.priorityLevel }}</span>
          </div>
        </div>

        <!-- 三防模块 -->
        <div v-else-if="isSecurityModule" class="info-section">
          <div class="stat-card">
            <div class="stat-number">{{ data.content.monitorCount }}</div>
            <div class="stat-label">监控设备数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ data.content.securityPersonnel }}</div>
            <div class="stat-label">配备保安人数</div>
          </div>
        </div>

        <!-- 专职保卫 -->
        <div v-else-if="isSecurityGuard" class="info-section">
          <div class="contact-card">
            <div class="contact-avatar">👮</div>
            <div class="contact-info">
              <div class="contact-name">{{ data.content.name }}</div>
              <div class="contact-phone">{{ data.content.phone }}</div>
            </div>
          </div>
        </div>

        <!-- 案件信息 -->
        <div v-else-if="isCaseInfo" class="info-section">
          <div class="year-comparison">
            <div class="year-section">
              <h4>今年案件</h4>
              <div class="case-stats">
                <div class="case-item">
                  <span class="case-type">刑事案件</span>
                  <span class="case-count">{{ data.content.thisYear.criminal }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">行政案件</span>
                  <span class="case-count">{{ data.content.thisYear.administrative }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">盗窃案件</span>
                  <span class="case-count">{{ data.content.thisYear.theft }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">抢劫案件</span>
                  <span class="case-count">{{ data.content.thisYear.robbery }}</span>
                </div>
              </div>
            </div>
            <div class="year-section">
              <h4>去年案件</h4>
              <div class="case-stats">
                <div class="case-item">
                  <span class="case-type">刑事案件</span>
                  <span class="case-count">{{ data.content.lastYear.criminal }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">行政案件</span>
                  <span class="case-count">{{ data.content.lastYear.administrative }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">盗窃案件</span>
                  <span class="case-count">{{ data.content.lastYear.theft }}</span>
                </div>
                <div class="case-item">
                  <span class="case-type">抢劫案件</span>
                  <span class="case-count">{{ data.content.lastYear.robbery }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 行业信息 -->
        <div v-else-if="isIndustryInfo" class="info-section">
          <div class="industry-grid">
            <div v-for="(value, key) in data.content.data" :key="key" class="industry-item">
              <div class="industry-value">{{ value }}</div>
              <div class="industry-label">{{ getIndustryLabel(key) }}</div>
            </div>
          </div>
        </div>

        <!-- 背审情况 -->
        <div v-else-if="isBackgroundCheck" class="info-section">
          <div class="progress-section">
            <div class="progress-item">
              <div class="progress-label">已完成背审人数</div>
              <div class="progress-value">{{ data.content.completedCount }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">无风险人员</div>
              <div class="progress-value success">{{ data.content.noRiskCount }}</div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${(data.content.noRiskCount / data.content.completedCount) * 100}%` }"></div>
            </div>
          </div>
        </div>

        <!-- 工作绩效 -->
        <div v-else-if="isWorkPerformance" class="info-section">
          <div class="performance-section">
            <h4>负责民警</h4>
            <div class="officer-list">
              <div v-for="officer in data.content.policeOfficers" :key="officer.name" class="officer-item">
                <span class="officer-name">{{ officer.name }}</span>
                <span class="officer-position">{{ officer.position }}</span>
              </div>
            </div>
            <h4>检查情况</h4>
            <div class="inspection-info">
              <div class="inspection-item">
                <span class="label">检查频次：</span>
                <span class="value">{{ data.content.inspectionCompletion.frequency }}</span>
              </div>
              <div class="inspection-item">
                <span class="label">完成率：</span>
                <span class="value success">{{ data.content.inspectionCompletion.completionRate }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 风险评估 -->
        <div v-else-if="isRiskAssessment" class="info-section">
          <div class="risk-section">
            <div class="risk-score">
              <div class="score-circle" :class="getRiskLevelClass()">
                <span class="score-number">{{ data.content.score }}</span>
                <span class="score-label">分</span>
              </div>
              <div class="risk-level">{{ getRiskLevelText() }}</div>
            </div>
            <div class="risk-issues">
              <h4>存在问题</h4>
              <ul class="issue-list">
                <li v-for="issue in data.content.issues" :key="issue" class="issue-item">
                  {{ issue }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 连线信息 -->
        <div v-else-if="isConnectionInfo" class="info-section">
          <div class="connection-section">
            <div class="connection-header">
              <h4>数据关联</h4>
              <div class="connection-status">
                <span class="status-indicator active"></span>
                实时同步
              </div>
            </div>
            <div class="connection-details">
              <div class="connection-item">
                <div class="connection-label">源节点：</div>
                <div class="connection-value">{{ getModuleName(data.content.connection.from) }}</div>
              </div>
              <div class="connection-item">
                <div class="connection-label">目标节点：</div>
                <div class="connection-value">{{ getModuleName(data.content.connection.to) }}</div>
              </div>
              <div class="connection-item">
                <div class="connection-label">数据流向：</div>
                <div class="connection-value">双向同步</div>
              </div>
              <div class="connection-item">
                <div class="connection-label">更新频率：</div>
                <div class="connection-value">实时</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ModalData } from '@/types';

interface Props {
  data: ModalData;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  close: [];
}>();

// 计算属性判断数据类型
const isBasicInfo = computed(() => props.data.content?.unitName !== undefined);
const isSecurityModule = computed(() => props.data.content?.monitorCount !== undefined);
const isSecurityGuard = computed(() => props.data.content?.name !== undefined && props.data.content?.phone !== undefined);
const isCaseInfo = computed(() => props.data.content?.thisYear !== undefined);
const isIndustryInfo = computed(() => props.data.content?.type !== undefined);
const isBackgroundCheck = computed(() => props.data.content?.completedCount !== undefined);
const isWorkPerformance = computed(() => props.data.content?.policeOfficers !== undefined);
const isRiskAssessment = computed(() => props.data.content?.score !== undefined);
const isConnectionInfo = computed(() => props.data.content?.connection !== undefined);

// 模态框样式
const modalStyle = computed(() => ({
  left: `${Math.min(props.data.position.x + 50, window.innerWidth - 400)}px`,
  top: `${Math.min(props.data.position.y - 100, window.innerHeight - 300)}px`,
}));

// 获取行业标签
const getIndustryLabel = (key: string) => {
  const labels: Record<string, string> = {
    bedCount: '床位数',
    doctorCount: '医生数',
    nurseCount: '护士数',
    dailyPatients: '日均患者',
    emergencyDept: '急诊科',
    specialDepts: '专科数量'
  };
  return labels[key] || key;
};

// 获取风险等级样式
const getRiskLevelClass = () => {
  const level = props.data.content?.level;
  return {
    'risk-low': level === 'low',
    'risk-medium': level === 'medium',
    'risk-high': level === 'high'
  };
};

// 获取风险等级文本
const getRiskLevelText = () => {
  const level = props.data.content?.level;
  const texts = {
    low: '低风险',
    medium: '中等风险',
    high: '高风险'
  };
  return texts[level as keyof typeof texts] || '未知';
};

// 获取模块名称
const getModuleName = (moduleId: string) => {
  const moduleNames: Record<string, string> = {
    basic: '基本信息',
    security: '三防模块',
    guard: '专职保卫',
    case: '案件信息',
    industry: '行业信息',
    background: '背审情况',
    performance: '工作绩效',
    risk: '风险评估'
  };
  return moduleNames[moduleId] || moduleId;
};

// 处理遮罩点击
const handleOverlayClick = () => {
  emit('close');
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-container {
  position: absolute;
  width: 400px;
  max-height: 500px;
  background: linear-gradient(135deg, rgba(0, 51, 102, 0.95), rgba(0, 17, 34, 0.95));
  border: 2px solid var(--tech-blue);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--tech-blue);
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1), transparent);
}

.modal-title {
  margin: 0;
  color: var(--white);
  font-size: 1.2rem;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: var(--tech-blue);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(1.1);
}

.modal-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.info-section {
  color: var(--white);
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.label {
  color: var(--gray);
  font-size: 0.9rem;
}

.value {
  color: var(--white);
  font-weight: bold;
}

.priority {
  color: var(--warning-yellow);
}

.success {
  color: var(--success-green);
}

.stat-card {
  text-align: center;
  padding: 15px;
  margin: 10px 0;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--tech-blue);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--gray);
  margin-top: 5px;
}

.contact-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.contact-avatar {
  font-size: 2rem;
  margin-right: 15px;
}

.contact-name {
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--white);
  margin-bottom: 5px;
}

.contact-phone {
  color: var(--tech-blue);
  font-size: 0.9rem;
}

.year-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.year-section h4 {
  color: var(--tech-blue);
  margin-bottom: 10px;
  text-align: center;
}

.case-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.case-type {
  font-size: 0.8rem;
  color: var(--gray);
}

.case-count {
  font-weight: bold;
  color: var(--white);
}

.industry-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.industry-item {
  text-align: center;
  padding: 10px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 8px;
}

.industry-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--tech-blue);
}

.industry-label {
  font-size: 0.8rem;
  color: var(--gray);
  margin-top: 5px;
}

.progress-section {
  text-align: center;
}

.progress-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-green), var(--tech-blue));
  transition: width 0.3s ease;
}

.officer-list {
  margin-bottom: 15px;
}

.officer-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.officer-name {
  font-weight: bold;
  color: var(--white);
}

.officer-position {
  color: var(--tech-blue);
  font-size: 0.9rem;
}

.inspection-info {
  margin-top: 15px;
}

.inspection-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.risk-section {
  text-align: center;
}

.risk-score {
  margin-bottom: 20px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  border: 3px solid;
}

.risk-low {
  border-color: var(--success-green);
  background: rgba(78, 205, 196, 0.2);
}

.risk-medium {
  border-color: var(--warning-yellow);
  background: rgba(255, 230, 109, 0.2);
}

.risk-high {
  border-color: var(--danger-red);
  background: rgba(255, 107, 107, 0.2);
}

.score-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--white);
}

.score-label {
  font-size: 0.8rem;
  color: var(--gray);
}

.risk-level {
  font-size: 1rem;
  font-weight: bold;
  color: var(--tech-blue);
}

.risk-issues {
  text-align: left;
}

.risk-issues h4 {
  color: var(--tech-blue);
  margin-bottom: 10px;
}

.issue-list {
  list-style: none;
  padding: 0;
}

.issue-item {
  padding: 5px 0;
  color: var(--gray);
  font-size: 0.9rem;
  position: relative;
  padding-left: 15px;
}

.issue-item::before {
  content: '•';
  color: var(--danger-red);
  position: absolute;
  left: 0;
}

/* 连线信息样式 */
.connection-section {
  text-align: left;
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.connection-header h4 {
  color: var(--tech-blue);
  margin: 0;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--success-green);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-green);
}

.status-indicator.active {
  animation: pulse 2s infinite;
}

.connection-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-label {
  color: var(--gray);
  font-size: 0.9rem;
}

.connection-value {
  color: var(--white);
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .modal-container {
    width: 350px;
    max-height: 450px;
  }
}

@media (max-width: 768px) {
  .modal-container {
    width: 300px;
    max-height: 400px;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .modal-header {
    padding: 15px;
  }

  .modal-title {
    font-size: 1.1rem;
  }

  .modal-content {
    padding: 15px;
    max-height: 320px;
  }

  .year-comparison {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .industry-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .score-circle {
    width: 60px;
    height: 60px;
  }

  .score-number {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .modal-container {
    width: calc(100vw - 40px);
    max-width: 280px;
    max-height: 350px;
  }

  .modal-header {
    padding: 12px;
  }

  .modal-title {
    font-size: 1rem;
  }

  .close-btn {
    font-size: 1.2rem;
  }

  .modal-content {
    padding: 12px;
    max-height: 270px;
  }

  .info-item {
    margin-bottom: 8px;
    padding: 6px 0;
  }

  .label,
  .value {
    font-size: 0.8rem;
  }

  .stat-card {
    padding: 10px;
    margin: 8px 0;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .contact-card {
    padding: 10px;
  }

  .contact-avatar {
    font-size: 1.5rem;
    margin-right: 10px;
  }

  .contact-name {
    font-size: 1rem;
  }

  .contact-phone {
    font-size: 0.8rem;
  }

  .case-item {
    padding: 3px 0;
  }

  .case-type,
  .case-count {
    font-size: 0.7rem;
  }

  .industry-item {
    padding: 8px;
  }

  .industry-value {
    font-size: 1rem;
  }

  .industry-label {
    font-size: 0.7rem;
  }

  .score-circle {
    width: 50px;
    height: 50px;
  }

  .score-number {
    font-size: 1rem;
  }

  .score-label {
    font-size: 0.7rem;
  }

  .officer-item {
    padding: 6px 0;
  }

  .officer-name {
    font-size: 0.8rem;
  }

  .officer-position {
    font-size: 0.7rem;
  }

  .connection-item {
    padding: 6px 0;
  }

  .connection-label,
  .connection-value {
    font-size: 0.8rem;
  }
}
</style>
