<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公安单位数据透视系统 - 可拖动版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a192f 0%, #0c213a 100%);
            color: #e6f1ff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
            background: 
                radial-gradient(circle at 10% 20%, rgba(21, 101, 192, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(33, 150, 243, 0.1) 20%);
        }

        /* 中心单位卡片 */
        .center-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 320px;
            height: 280px;
            background: linear-gradient(145deg, rgba(25, 118, 210, 0.2), rgba(33, 150, 243, 0.1));
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 0 30px rgba(33, 150, 243, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.05);
            z-index: 10;
            cursor: move;
            transition: all 0.3s ease;
        }

        .center-card:hover {
            transform: translate(-50%, -50%) scale(1.02);
            box-shadow: 
                0 0 50px rgba(33, 150, 243, 0.4),
                inset 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .unit-name {
            font-size: 24px;
            font-weight: bold;
            color: #64b5f6;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
        }

        .unit-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .info-item {
            font-size: 14px;
            line-height: 1.6;
        }

        .info-label {
            color: #90caf9;
            font-weight: 500;
        }

        .info-value {
            color: #e3f2fd;
            font-weight: 300;
        }

        .priority-level {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            background: linear-gradient(45deg, #f44336, #ff5722);
            color: white;
            margin-left: 8px;
        }

        /* 周围信息卡片 */
        .info-card {
            position: absolute;
            width: 280px;
            min-height: 200px;
            background: linear-gradient(145deg, rgba(13, 71, 161, 0.3), rgba(21, 101, 192, 0.2));
            border: 1px solid rgba(33, 150, 243, 0.2);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(8px);
            box-shadow: 
                0 0 20px rgba(33, 150, 243, 0.15),
                inset 0 0 15px rgba(255, 255, 255, 0.03);
            cursor: move;
            transition: all 0.3s ease;
            user-select: none;
        }

        .info-card.dragging {
            opacity: 0.8;
            transform: scale(1.02);
            box-shadow: 
                0 0 40px rgba(33, 150, 243, 0.5),
                inset 0 0 25px rgba(255, 255, 255, 0.1);
            z-index: 1000 !important;
        }

        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            cursor: move;
        }

        .card-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #4fc3f7, transparent);
        }

        .card-content {
            font-size: 14px;
            line-height: 1.8;
        }

        .card-item {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(144, 202, 249, 0.2);
        }

        .card-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .item-label {
            color: #90caf9;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .item-value {
            color: #e3f2fd;
            font-weight: 300;
        }

        .highlight {
            color: #4fc3f7;
            font-weight: bold;
        }

        .warning {
            color: #ff8a65;
            font-weight: bold;
        }

        .success {
            color: #81c784;
            font-weight: bold;
        }

        /* 连接线 */
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.4), transparent);
            transform-origin: 0 0;
            z-index: 1;
            pointer-events: none;
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .float {
            animation: float 3s ease-in-out infinite;
        }

        /* 背景粒子效果 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(100, 181, 246, 0.6);
            border-radius: 50%;
            animation: particle-float linear infinite;
        }

        @keyframes particle-float {
            to {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 控制按钮 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .control-btn {
            background: rgba(25, 118, 210, 0.3);
            border: 1px solid rgba(33, 150, 243, 0.5);
            color: #64b5f6;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            margin-left: 10px;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(33, 150, 243, 0.5);
            transform: translateY(-2px);
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .info-card {
                width: 240px;
                min-height: 180px;
                padding: 15px;
            }
            
            .center-card {
                width: 280px;
                height: 250px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="particles" id="particles"></div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button class="control-btn" onclick="resetLayout()">重置布局</button>
            <button class="control-btn" onclick="saveLayout()">保存布局</button>
        </div>
        
        <!-- 中心单位卡片 -->
        <div class="center-card draggable" id="centerCard">
            <div class="unit-name">北京市第一人民医院</div>
            <div class="unit-info">
                <div class="info-item">
                    <div class="info-label">所属区县：</div>
                    <div class="info-value">北京市朝阳区</div>
                </div>
                <div class="info-item">
                    <div class="info-label">地址：</div>
                    <div class="info-value">朝阳区建国路123号</div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属派出所：</div>
                    <div class="info-value">朝阳分局建国门派出所</div>
                </div>
                <div class="info-item">
                    <div class="info-label">重点等级：</div>
                    <div class="info-value">一级重点 <span class="priority-level">高危</span></div>
                </div>
            </div>
        </div>

        <!-- 周围信息卡片 -->
        <div class="info-card draggable" id="sanfangCard" style="top: 20%; left: 15%;">
            <div class="card-title">三防信息</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">监控数量：</div>
                    <div class="item-value highlight">156个</div>
                </div>
                <div class="card-item">
                    <div class="item-label">配备保安人数：</div>
                    <div class="item-value highlight">24人</div>
                </div>
                <div class="card-item">
                    <div class="item-label">安防设备状态：</div>
                    <div class="item-value success">全部正常运行</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="securityCard" style="top: 20%; right: 15%;">
            <div class="card-title">专职保卫</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">负责人姓名：</div>
                    <div class="item-value">张建国</div>
                </div>
                <div class="card-item">
                    <div class="item-label">联系方式：</div>
                    <div class="item-value">138****8888</div>
                </div>
                <div class="card-item">
                    <div class="item-label">备用联系人：</div>
                    <div class="item-value">李志强 139****9999</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="caseCard" style="bottom: 20%; left: 15%;">
            <div class="card-title">案件信息</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">今年刑事案件：</div>
                    <div class="item-value highlight">12件</div>
                </div>
                <div class="card-item">
                    <div class="item-label">今年行政案件：</div>
                    <div class="item-value">8件</div>
                </div>
                <div class="card-item">
                    <div class="item-label">主要类型：</div>
                    <div class="item-value">盗窃6件，抢劫2件</div>
                </div>
                <div class="card-item">
                    <div class="item-label">去年同期：</div>
                    <div class="item-value">刑事15件，行政12件</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="industryCard" style="bottom: 20%; right: 15%;">
            <div class="card-title">行业信息</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">学生人数：</div>
                    <div class="item-value">0人</div>
                </div>
                <div class="card-item">
                    <div class="item-label">教职工人数：</div>
                    <div class="item-value">0人</div>
                </div>
                <div class="card-item">
                    <div class="item-label">医护人员：</div>
                    <div class="item-value highlight">1,247人</div>
                </div>
                <div class="card-item">
                    <div class="item-label">日均门诊量：</div>
                    <div class="item-value">2,800人次</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="backgroundCard" style="top: 50%; left: 5%;">
            <div class="card-title">背审情况</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">已完成背审：</div>
                    <div class="item-value highlight">89人</div>
                </div>
                <div class="card-item">
                    <div class="item-label">风险评估：</div>
                    <div class="item-value success">无风险人员</div>
                </div>
                <div class="card-item">
                    <div class="item-label">待审核：</div>
                    <div class="item-value">12人</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="performanceCard" style="top: 50%; right: 5%;">
            <div class="card-title">工作绩效</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">责任民警：</div>
                    <div class="item-value">王建国（警号：123456）</div>
                </div>
                <div class="card-item">
                    <div class="item-label">检查频次：</div>
                    <div class="item-value highlight">每月2次例行检查</div>
                </div>
                <div class="card-item">
                    <div class="item-label">本月完成：</div>
                    <div class="item-value">2次安全检查</div>
                </div>
                <div class="card-item">
                    <div class="item-label">发现问题：</div>
                    <div class="item-value">3项已整改</div>
                </div>
            </div>
        </div>

        <div class="info-card draggable" id="riskCard" style="top: 10%; left: 50%; transform: translateX(-50%);">
            <div class="card-title">风险评估</div>
            <div class="card-content">
                <div class="card-item">
                    <div class="item-label">评估分数：</div>
                    <div class="item-value highlight">85分</div>
                </div>
                <div class="card-item">
                    <div class="item-label">风险等级：</div>
                    <div class="item-value warning">中等风险</div>
                </div>
                <div class="card-item">
                    <div class="item-label">主要问题：</div>
                    <div class="item-value">夜间安保力量不足</div>
                </div>
                <div class="card-item">
                    <div class="item-label">整改建议：</div>
                    <div class="item-value">增加夜班保安2人</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建背景粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置和大小
                const size = Math.random() * 3 + 1;
                const left = Math.random() * 100;
                const top = Math.random() * 100;
                const duration = Math.random() * 10 + 5;
                const delay = Math.random() * 5;
                
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = left + '%';
                particle.style.top = top + '%';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';
                
                particlesContainer.appendChild(particle);
            }
        }

        // 拖拽功能实现
        let isDragging = false;
        let currentElement = null;
        let offsetX, offsetY;
        let connectionLines = [];

        function makeDraggable() {
            const draggables = document.querySelectorAll('.draggable');
            
            draggables.forEach(element => {
                element.addEventListener('mousedown', startDrag);
            });

            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
        }

        function startDrag(e) {
            // 如果点击的是输入框或按钮，不触发拖拽
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'BUTTON') {
                return;
            }
            
            isDragging = true;
            currentElement = this;
            currentElement.classList.add('dragging');
            
            const rect = currentElement.getBoundingClientRect();
            offsetX = e.clientX - rect.left;
            offsetY = e.clientY - rect.top;
            
            // 阻止默认行为
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging || !currentElement) return;
            
            const x = e.clientX - offsetX;
            const y = e.clientY - offsetY;
            
            currentElement.style.left = x + 'px';
            currentElement.style.top = y + 'px';
            currentElement.style.transform = 'none'; // 清除原有的transform
            
            // 更新连接线
            updateConnectionLines();
        }

        function stopDrag() {
            if (currentElement) {
                currentElement.classList.remove('dragging');
            }
            isDragging = false;
            currentElement = null;
        }

        // 创建连接线
        function createConnectionLines() {
            // 清除现有的连接线
            document.querySelectorAll('.connection-line').forEach(line => line.remove());
            connectionLines = [];
            
            const centerCard = document.getElementById('centerCard');
            const cards = [
                'sanfangCard', 'securityCard', 'caseCard', 
                'industryCard', 'backgroundCard', 'performanceCard', 'riskCard'
            ];
            
            cards.forEach(cardId => {
                const card = document.getElementById(cardId);
                const line = document.createElement('div');
                line.className = 'connection-line';
                line.dataset.from = 'centerCard';
                line.dataset.to = cardId;
                
                document.querySelector('.container').appendChild(line);
                connectionLines.push(line);
            });
            
            updateConnectionLines();
        }

        // 更新连接线位置
        function updateConnectionLines() {
            const centerCard = document.getElementById('centerCard');
            if (!centerCard) return;
            
            const centerRect = centerCard.getBoundingClientRect();
            const centerX = centerRect.left + centerRect.width / 2;
            const centerY = centerRect.top + centerRect.height / 2;
            
            document.querySelectorAll('.connection-line').forEach(line => {
                const toCardId = line.dataset.to;
                const toCard = document.getElementById(toCardId);
                
                if (toCard) {
                    const toRect = toCard.getBoundingClientRect();
                    const toX = toRect.left + toRect.width / 2;
                    const toY = toRect.top + toRect.height / 2;
                    
                    const distance = Math.sqrt(Math.pow(toX - centerX, 2) + Math.pow(toY - centerY, 2));
                    const angle = Math.atan2(toY - centerY, toX - centerX) * 180 / Math.PI;
                    
                    line.style.width = distance + 'px';
                    line.style.height = '1px';
                    line.style.left = centerX + 'px';
                    line.style.top = centerY + 'px';
                    line.style.transform = `rotate(${angle}deg)`;
                }
            });
        }

        // 卡片动画显示
        function showCards() {
            const cards = document.querySelectorAll('.info-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                    card.classList.add('pulse');
                }, index * 200);
            });
        }

        // 卡片交互效果
        function setupCardInteractions() {
            const cards = document.querySelectorAll('.info-card, .center-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('dragging')) {
                        this.style.transform = 'scale(1.02)';
                        this.style.boxShadow = '0 0 40px rgba(33, 150, 243, 0.5), inset 0 0 25px rgba(255, 255, 255, 0.1)';
                    }
                });
                
                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('dragging')) {
                        this.style.transform = 'scale(1)';
                        this.style.boxShadow = '0 0 20px rgba(33, 150, 243, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.03)';
                    }
                });
            });
        }

        // 重置布局
        function resetLayout() {
            const defaultPositions = {
                sanfangCard: { top: '20%', left: '15%' },
                securityCard: { top: '20%', right: '15%', left: 'auto' },
                caseCard: { bottom: '20%', left: '15%', top: 'auto' },
                industryCard: { bottom: '20%', right: '15%', left: 'auto', top: 'auto' },
                backgroundCard: { top: '50%', left: '5%' },
                performanceCard: { top: '50%', right: '5%', left: 'auto' },
                riskCard: { top: '10%', left: '50%', transform: 'translateX(-50%)' }
            };
            
            Object.keys(defaultPositions).forEach(cardId => {
                const card = document.getElementById(cardId);
                if (card) {
                    Object.keys(defaultPositions[cardId]).forEach(prop => {
                        card.style[prop] = defaultPositions[cardId][prop];
                    });
                }
            });
            
            // 重置中心卡片位置
            const centerCard = document.getElementById('centerCard');
            centerCard.style.top = '50%';
            centerCard.style.left = '50%';
            centerCard.style.transform = 'translate(-50%, -50%)';
            
            // 更新连接线
            setTimeout(updateConnectionLines, 100);
        }

        // 保存布局
        function saveLayout() {
            const layout = {};
            const draggables = document.querySelectorAll('.draggable');
            
            draggables.forEach(element => {
                const id = element.id;
                const rect = element.getBoundingClientRect();
                layout[id] = {
                    left: rect.left,
                    top: rect.top,
                    width: rect.width,
                    height: rect.height
                };
            });
            
            // 保存到localStorage
            localStorage.setItem('dashboardLayout', JSON.stringify(layout));
            
            // 显示保存成功提示
            alert('布局已保存！');
        }

        // 加载保存的布局
        function loadLayout() {
            const savedLayout = localStorage.getItem('dashboardLayout');
            if (savedLayout) {
                const layout = JSON.parse(savedLayout);
                Object.keys(layout).forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        const pos = layout[id];
                        element.style.left = pos.left + 'px';
                        element.style.top = pos.top + 'px';
                        element.style.transform = 'none';
                    }
                });
            }
        }

        // 数据更新模拟
        function updateData() {
            // 这里可以添加实时数据更新逻辑
            setInterval(() => {
                // 模拟数据更新效果
                const highlightNumbers = document.querySelectorAll('.highlight');
                highlightNumbers.forEach(num => {
                    num.classList.add('pulse');
                    setTimeout(() => {
                        num.classList.remove('pulse');
                    }, 1000);
                });
            }, 10000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            makeDraggable();
            setTimeout(createConnectionLines, 1000);
            setTimeout(showCards, 500);
            setupCardInteractions();
            updateData();
            loadLayout(); // 加载保存的布局
            
            // 响应式调整
            window.addEventListener('resize', function() {
                setTimeout(updateConnectionLines, 100);
            });
        });
    </script>
</body>
</html>