@import './base.css';

/* 公安系统配色方案 */
:root {
  --primary-blue: #003366;
  --tech-blue: #00D4FF;
  --dark-blue: #001122;
  --light-blue: #4A90E2;
  --accent-blue: #0099CC;
  --white: #FFFFFF;
  --gray: #CCCCCC;
  --dark-gray: #333333;
  --glow-blue: #00D4FF;
  --success-green: #4ECDC4;
  --warning-yellow: #FFE66D;
  --danger-red: #FF6B6B;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, var(--dark-blue) 0%, var(--primary-blue) 50%, var(--dark-blue) 100%);
  color: var(--white);
  overflow: hidden;
  height: 100vh;
}

#app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 科技感发光效果 */
.glow {
  box-shadow: 0 0 10px var(--glow-blue), 0 0 20px var(--glow-blue), 0 0 30px var(--glow-blue);
}

.glow-text {
  text-shadow: 0 0 5px var(--glow-blue), 0 0 10px var(--glow-blue);
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse {
  animation: pulse 2s infinite;
}

.float {
  animation: float 3s ease-in-out infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-blue);
}

::-webkit-scrollbar-thumb {
  background: var(--tech-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--light-blue);
}

/* 按钮样式 */
.btn {
  background: linear-gradient(45deg, var(--tech-blue), var(--light-blue));
  border: none;
  color: var(--white);
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

/* 卡片样式 */
.card {
  background: rgba(0, 51, 102, 0.8);
  border: 1px solid var(--tech-blue);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: var(--glow-blue);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}
