<template>
  <div class="info-panel" :class="{ 'collapsed': isCollapsed }">
    <div class="panel-header" @click="togglePanel">
      <h3 class="panel-title">
        <span class="icon">📊</span>
        系统概览
      </h3>
      <button class="toggle-btn">
        {{ isCollapsed ? '▶' : '◀' }}
      </button>
    </div>
    
    <div class="panel-content" v-show="!isCollapsed">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">🏥</div>
          <div class="stat-info">
            <div class="stat-value">1</div>
            <div class="stat-label">监管单位</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">📹</div>
          <div class="stat-info">
            <div class="stat-value">156</div>
            <div class="stat-label">监控设备</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">👮</div>
          <div class="stat-info">
            <div class="stat-value">24</div>
            <div class="stat-label">安保人员</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">📋</div>
          <div class="stat-info">
            <div class="stat-value">10</div>
            <div class="stat-label">今年案件</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-value">95%</div>
            <div class="stat-label">检查完成率</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <div class="stat-value">85</div>
            <div class="stat-label">风险评分</div>
          </div>
        </div>
      </div>
      
      <div class="recent-activities">
        <h4 class="section-title">最近活动</h4>
        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-time">10:30</div>
            <div class="activity-desc">完成例行安全检查</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">09:15</div>
            <div class="activity-desc">更新监控设备状态</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">08:45</div>
            <div class="activity-desc">背景审查数据同步</div>
          </div>
        </div>
      </div>
      
      <div class="system-status">
        <h4 class="section-title">系统状态</h4>
        <div class="status-list">
          <div class="status-item">
            <span class="status-dot online"></span>
            <span class="status-text">数据连接正常</span>
          </div>
          <div class="status-item">
            <span class="status-dot online"></span>
            <span class="status-text">监控系统在线</span>
          </div>
          <div class="status-item">
            <span class="status-dot online"></span>
            <span class="status-text">实时同步活跃</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const isCollapsed = ref(false);

const togglePanel = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped>
.info-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 280px;
  background: linear-gradient(135deg, rgba(0, 51, 102, 0.95), rgba(0, 17, 34, 0.95));
  border: 2px solid var(--tech-blue);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
  overflow: hidden;
}

.info-panel.collapsed {
  width: 60px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--tech-blue);
  cursor: pointer;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1), transparent);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: var(--white);
  font-size: 1.1rem;
  font-weight: bold;
}

.collapsed .panel-title .icon {
  font-size: 1.5rem;
}

.collapsed .panel-title span:not(.icon) {
  display: none;
}

.toggle-btn {
  background: none;
  border: none;
  color: var(--tech-blue);
  font-size: 1rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(0, 212, 255, 0.2);
}

.panel-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--tech-blue);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--gray);
}

.section-title {
  color: var(--tech-blue);
  font-size: 1rem;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  padding-bottom: 5px;
}

.recent-activities {
  margin-bottom: 20px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  display: flex;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-time {
  color: var(--tech-blue);
  font-size: 0.8rem;
  font-weight: bold;
  min-width: 40px;
}

.activity-desc {
  color: var(--white);
  font-size: 0.8rem;
  flex: 1;
}

.system-status {
  margin-bottom: 10px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-green);
}

.status-dot.online {
  background: var(--success-green);
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background: var(--danger-red);
}

.status-text {
  color: var(--white);
  font-size: 0.8rem;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 51, 102, 0.3);
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--tech-blue);
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .info-panel {
    width: 260px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .info-panel {
    width: 240px;
    top: 15px;
    left: 15px;
  }

  .info-panel.collapsed {
    width: 50px;
  }

  .panel-header {
    padding: 12px 15px;
  }

  .panel-title {
    font-size: 1rem;
  }

  .panel-content {
    padding: 15px;
  }

  .stat-item {
    padding: 8px;
  }

  .stat-icon {
    font-size: 1.2rem;
  }

  .stat-value {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .info-panel {
    width: calc(100vw - 30px);
    max-width: 200px;
    top: 10px;
    left: 10px;
  }

  .info-panel.collapsed {
    width: 45px;
  }

  .panel-header {
    padding: 10px 12px;
  }

  .panel-title {
    font-size: 0.9rem;
    gap: 8px;
  }

  .panel-content {
    padding: 12px;
    max-height: 300px;
  }

  .stats-grid {
    gap: 8px;
  }

  .stat-item {
    padding: 6px;
    gap: 8px;
  }

  .stat-icon {
    font-size: 1rem;
  }

  .stat-value {
    font-size: 0.9rem;
  }

  .stat-label {
    font-size: 0.6rem;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .activity-desc,
  .status-text {
    font-size: 0.7rem;
  }

  .activity-time {
    font-size: 0.7rem;
    min-width: 35px;
  }
}
</style>
