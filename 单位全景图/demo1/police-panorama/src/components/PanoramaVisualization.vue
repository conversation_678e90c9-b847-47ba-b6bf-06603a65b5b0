<template>
  <div class="panorama-container" ref="containerRef">
    <!-- 粒子背景 -->
    <ParticleBackground />

    <!-- SVG 连线层 -->
    <svg class="connections-layer" :width="containerWidth" :height="containerHeight">
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        <filter id="strongGlow">
          <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:0.3" />
          <stop offset="50%" style="stop-color:#00D4FF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#00D4FF;stop-opacity:0.3" />
        </linearGradient>

        <linearGradient id="pulseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:0">
            <animate attributeName="stop-opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
          </stop>
          <stop offset="50%" style="stop-color:#FFFFFF;stop-opacity:1">
            <animate attributeName="stop-opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite"/>
          </stop>
          <stop offset="100%" style="stop-color:#00D4FF;stop-opacity:0">
            <animate attributeName="stop-opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
          </stop>
        </linearGradient>

        <radialGradient id="nodeGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#00D4FF;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#003366;stop-opacity:0.9" />
        </radialGradient>
      </defs>
      
      <g v-for="connection in visibleConnections" :key="`${connection.from}-${connection.to}`">
        <!-- 底层发光线 -->
        <line
          :x1="getNodePosition(connection.from).x"
          :y1="getNodePosition(connection.from).y"
          :x2="getNodePosition(connection.to).x"
          :y2="getNodePosition(connection.to).y"
          stroke="url(#lineGradient)"
          stroke-width="6"
          filter="url(#strongGlow)"
          opacity="0.3"
          :class="{ 'animated-line': connection.animated }"
        />

        <!-- 主连线 -->
        <line
          :x1="getNodePosition(connection.from).x"
          :y1="getNodePosition(connection.from).y"
          :x2="getNodePosition(connection.to).x"
          :y2="getNodePosition(connection.to).y"
          :stroke="connection.animated ? 'url(#pulseGradient)' : connection.color"
          stroke-width="2"
          filter="url(#glow)"
          :class="{
            'animated-line': connection.animated,
            'hovered-line': hoveredConnection === `${connection.from}-${connection.to}`
          }"
          :data-connection="`${connection.from}-${connection.to}`"
          @click="onConnectionClick(connection)"
          @mouseenter="onConnectionHover(connection, true)"
          @mouseleave="onConnectionHover(connection, false)"
        />
      </g>
    </svg>

    <!-- 节点层 -->
    <div class="nodes-layer">
      <!-- 中心节点 -->
      <div
        class="node center-node"
        :class="{
          'hovered': hoveredNode === centerModule.id,
          'selected': selectedNode === centerModule.id
        }"
        :style="getCenterNodeStyle()"
        :data-node-id="centerModule.id"
        @click="onNodeClick(centerModule)"
        @mouseenter="onNodeHover(centerModule, true)"
        @mouseleave="onNodeHover(centerModule, false)"
      >
        <div class="node-content">
          <div class="node-icon">🏥</div>
          <div class="node-title">{{ centerModule.name }}</div>
          <div class="node-subtitle">{{ centerModule.data.unitName }}</div>
        </div>
        <div class="node-glow"></div>
        <div class="node-pulse"></div>
      </div>

      <!-- 周围节点 -->
      <div
        v-for="module in surroundingModules"
        :key="module.id"
        class="node surrounding-node"
        :class="{
          'hovered': hoveredNode === module.id,
          'selected': selectedNode === module.id
        }"
        :style="getNodeStyle(module)"
        :data-node-id="module.id"
        @click="onNodeClick(module)"
        @mouseenter="onNodeHover(module, true)"
        @mouseleave="onNodeHover(module, false)"
      >
        <div class="node-content">
          <div class="node-icon">{{ getNodeIcon(module.type) }}</div>
          <div class="node-title">{{ module.name }}</div>
          <div class="node-data">{{ getNodeSummary(module) }}</div>
        </div>
        <div class="node-glow" :style="{ backgroundColor: module.color }"></div>
        <div class="node-pulse" :style="{ borderColor: module.color }"></div>
      </div>
    </div>

    <!-- 信息面板 -->
    <InfoPanel />

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <button class="control-btn" @click="resetView" title="重置视图 (R)">
          🔄
        </button>
        <div class="zoom-info">{{ Math.round(scale * 100) }}%</div>
      </div>
      <div class="control-group">
        <div class="coordinate-info">
          X: {{ Math.round(translateX) }}, Y: {{ Math.round(translateY) }}
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <DetailModal
      v-if="modalData.visible"
      :data="modalData"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import type { DataModule, Connection, ModalData } from '@/types';
import { dataModules, connections } from '@/data/mockData';
import DetailModal from './DetailModal.vue';
import ParticleBackground from './ParticleBackground.vue';
import InfoPanel from './InfoPanel.vue';

// 响应式数据
const containerRef = ref<HTMLElement>();
const containerWidth = ref(window.innerWidth);
const containerHeight = ref(window.innerHeight);
const scale = ref(1);
const translateX = ref(0);
const translateY = ref(0);

// 模块数据
const centerModule = computed(() => dataModules.find(m => m.type === 'basic')!);
const surroundingModules = computed(() => dataModules.filter(m => m.type !== 'basic'));
const visibleConnections = computed(() => connections);

// 弹窗数据
const modalData = reactive<ModalData>({
  title: '',
  content: null,
  position: { x: 0, y: 0 },
  visible: false
});

// 获取节点位置
const getNodePosition = (nodeId: string) => {
  const module = dataModules.find(m => m.id === nodeId);
  if (!module) return { x: 0, y: 0 };
  
  return {
    x: containerWidth.value / 2 + module.position.x * scale.value + translateX.value,
    y: containerHeight.value / 2 + module.position.y * scale.value + translateY.value
  };
};

// 获取中心节点样式
const getCenterNodeStyle = () => {
  const pos = getNodePosition('basic');
  return {
    left: `${pos.x - 80}px`,
    top: `${pos.y - 80}px`,
    transform: `scale(${scale.value})`,
  };
};

// 获取节点样式
const getNodeStyle = (module: DataModule) => {
  const pos = getNodePosition(module.id);
  return {
    left: `${pos.x - 60}px`,
    top: `${pos.y - 60}px`,
    transform: `scale(${scale.value})`,
    borderColor: module.color,
  };
};

// 获取节点图标
const getNodeIcon = (type: string) => {
  const icons: Record<string, string> = {
    security: '🛡️',
    guard: '👮',
    case: '📋',
    industry: '🏢',
    background: '🔍',
    performance: '📊',
    risk: '⚠️'
  };
  return icons[type] || '📄';
};

// 获取节点摘要
const getNodeSummary = (module: DataModule) => {
  switch (module.type) {
    case 'security':
      return `监控${module.data.monitorCount}个`;
    case 'guard':
      return module.data.name;
    case 'case':
      return `今年${module.data.thisYear.criminal + module.data.thisYear.administrative}起`;
    case 'industry':
      return `床位${module.data.data.bedCount}张`;
    case 'background':
      return `已审${module.data.completedCount}人`;
    case 'performance':
      return `完成率${module.data.inspectionCompletion.completionRate}%`;
    case 'risk':
      return `评分${module.data.score}分`;
    default:
      return '';
  }
};

// 交互状态
const hoveredNode = ref<string | null>(null);
const hoveredConnection = ref<string | null>(null);
const selectedNode = ref<string | null>(null);

// 事件处理
const onNodeClick = (module: DataModule) => {
  selectedNode.value = module.id;
  modalData.title = module.name;
  modalData.content = module.data;
  modalData.position = getNodePosition(module.id);
  modalData.visible = true;

  // 添加点击动画效果
  const nodeElement = document.querySelector(`[data-node-id="${module.id}"]`);
  if (nodeElement) {
    nodeElement.classList.add('clicked');
    setTimeout(() => {
      nodeElement.classList.remove('clicked');
    }, 300);
  }
};

const onNodeHover = (module: DataModule, isHover: boolean) => {
  hoveredNode.value = isHover ? module.id : null;

  // 高亮相关连线
  if (isHover) {
    const relatedConnections = connections.filter(
      conn => conn.from === module.id || conn.to === module.id
    );
    relatedConnections.forEach(conn => {
      const lineElement = document.querySelector(`[data-connection="${conn.from}-${conn.to}"]`);
      if (lineElement) {
        lineElement.classList.add('highlighted');
      }
    });
  } else {
    // 移除所有高亮
    document.querySelectorAll('.highlighted').forEach(el => {
      el.classList.remove('highlighted');
    });
  }
};

const onConnectionClick = (connection: Connection) => {
  // 点击连线时显示连接的两个节点信息
  const fromModule = dataModules.find(m => m.id === connection.from);
  const toModule = dataModules.find(m => m.id === connection.to);

  if (fromModule && toModule) {
    modalData.title = `${fromModule.name} - ${toModule.name}`;
    modalData.content = {
      from: fromModule.data,
      to: toModule.data,
      connection: connection
    };
    modalData.position = {
      x: (getNodePosition(connection.from).x + getNodePosition(connection.to).x) / 2,
      y: (getNodePosition(connection.from).y + getNodePosition(connection.to).y) / 2
    };
    modalData.visible = true;
  }
};

const onConnectionHover = (connection: Connection, isHover: boolean) => {
  hoveredConnection.value = isHover ? `${connection.from}-${connection.to}` : null;

  // 高亮连接的节点
  if (isHover) {
    const fromElement = document.querySelector(`[data-node-id="${connection.from}"]`);
    const toElement = document.querySelector(`[data-node-id="${connection.to}"]`);
    if (fromElement) fromElement.classList.add('connected-highlight');
    if (toElement) toElement.classList.add('connected-highlight');
  } else {
    document.querySelectorAll('.connected-highlight').forEach(el => {
      el.classList.remove('connected-highlight');
    });
  }
};

const closeModal = () => {
  modalData.visible = false;
  selectedNode.value = null;
};

// 拖拽和缩放功能
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const lastTranslate = ref({ x: 0, y: 0 });

const handleMouseDown = (event: MouseEvent) => {
  if (event.target === containerRef.value) {
    isDragging.value = true;
    dragStart.value = { x: event.clientX, y: event.clientY };
    lastTranslate.value = { x: translateX.value, y: translateY.value };
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }
};

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    const deltaX = event.clientX - dragStart.value.x;
    const deltaY = event.clientY - dragStart.value.y;
    translateX.value = lastTranslate.value.x + deltaX;
    translateY.value = lastTranslate.value.y + deltaY;
  }
};

const handleMouseUp = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

const handleWheel = (event: WheelEvent) => {
  event.preventDefault();
  const delta = event.deltaY > 0 ? 0.9 : 1.1;
  const newScale = Math.max(0.5, Math.min(2, scale.value * delta));

  // 以鼠标位置为中心缩放
  const rect = containerRef.value?.getBoundingClientRect();
  if (rect) {
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;
    const centerX = containerWidth.value / 2;
    const centerY = containerHeight.value / 2;

    const deltaScale = newScale / scale.value;
    translateX.value = centerX + (translateX.value - centerX) * deltaScale + (mouseX - centerX) * (1 - deltaScale);
    translateY.value = centerY + (translateY.value - centerY) * deltaScale + (mouseY - centerY) * (1 - deltaScale);
  }

  scale.value = newScale;
};

// 重置视图
const resetView = () => {
  scale.value = 1;
  translateX.value = 0;
  translateY.value = 0;
};

// 窗口大小调整
const handleResize = () => {
  containerWidth.value = window.innerWidth;
  containerHeight.value = window.innerHeight;
};

// 键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal();
  } else if (event.key === 'r' || event.key === 'R') {
    resetView();
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  window.addEventListener('keydown', handleKeyDown);

  if (containerRef.value) {
    containerRef.value.addEventListener('mousedown', handleMouseDown);
    containerRef.value.addEventListener('wheel', handleWheel, { passive: false });
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('keydown', handleKeyDown);

  if (containerRef.value) {
    containerRef.value.removeEventListener('mousedown', handleMouseDown);
    containerRef.value.removeEventListener('wheel', handleWheel);
  }

  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
});
</script>

<style scoped>
.panorama-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: radial-gradient(circle at center, rgba(0, 51, 102, 0.3) 0%, rgba(0, 17, 34, 0.8) 100%);
  cursor: grab;
}

.panorama-container:active {
  cursor: grabbing;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.connections-layer line {
  pointer-events: stroke;
  cursor: pointer;
  transition: all 0.3s ease;
}

.connections-layer line:hover {
  stroke-width: 4;
  filter: url(#glow) brightness(1.5);
}

.hovered-line {
  stroke-width: 4 !important;
  filter: url(#glow) brightness(1.5) !important;
}

.highlighted {
  stroke-width: 3 !important;
  filter: url(#glow) brightness(1.2) !important;
  animation: pulse-line 1s ease-in-out infinite;
}

.animated-line {
  stroke-dasharray: 20 5;
  animation: data-flow 3s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

@keyframes pulse-line {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.nodes-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.node {
  position: absolute;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  animation: node-breathe 4s ease-in-out infinite;
}

.center-node {
  width: 160px;
  height: 160px;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), rgba(0, 212, 255, 0.2), rgba(0, 51, 102, 0.8));
  border: 3px solid var(--tech-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.center-node::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  animation: rotate 8s linear infinite;
  z-index: 0;
}

.surrounding-node {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(0, 51, 102, 0.8), rgba(0, 17, 34, 0.9));
  border: 2px solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.surrounding-node::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.05), transparent);
  animation: rotate 12s linear infinite;
  z-index: 0;
}

.node:hover {
  transform: scale(1.1) !important;
  z-index: 10;
}

.node:hover .node-glow {
  opacity: 1;
  transform: scale(1.2);
}

.node.hovered {
  transform: scale(1.15) !important;
  z-index: 15;
}

.node.hovered .node-glow {
  opacity: 1;
  transform: scale(1.3);
  animation: glow-pulse 1.5s ease-in-out infinite;
}

.node.selected {
  transform: scale(1.2) !important;
  z-index: 20;
}

.node.selected .node-pulse {
  opacity: 1;
  animation: pulse-ring 2s ease-out infinite;
}

.node.clicked {
  animation: click-effect 0.3s ease-out;
}

.node.connected-highlight {
  transform: scale(1.1) !important;
  z-index: 12;
}

.node.connected-highlight .node-glow {
  opacity: 0.8;
  transform: scale(1.2);
}

.node-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.node-icon {
  font-size: 2rem;
  margin-bottom: 5px;
}

.center-node .node-icon {
  font-size: 2.5rem;
}

.node-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: var(--white);
  margin-bottom: 2px;
}

.center-node .node-title {
  font-size: 1.1rem;
}

.node-subtitle,
.node-data {
  font-size: 0.7rem;
  color: var(--gray);
}

.node-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: var(--tech-blue);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;
  filter: blur(15px);
}

.node-pulse {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 2px solid var(--tech-blue);
  border-radius: 50%;
  opacity: 0;
  z-index: 0;
}

/* 动画关键帧 */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  50% {
    opacity: 1;
    transform: scale(1.4);
  }
}

@keyframes pulse-ring {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes click-effect {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1.1); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes data-flow {
  0% {
    stroke-dashoffset: 0;
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: -40;
    opacity: 0.8;
  }
}

@keyframes node-breathe {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.6);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .center-node {
    width: 120px;
    height: 120px;
  }

  .surrounding-node {
    width: 80px;
    height: 80px;
  }

  .node-title {
    font-size: 0.8rem;
  }

  .node-subtitle,
  .node-data {
    font-size: 0.6rem;
  }

  .node-icon {
    font-size: 1.5rem;
  }

  .center-node .node-icon {
    font-size: 2rem;
  }
}

/* 控制面板样式 */
.control-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 100;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 51, 102, 0.9);
  border: 1px solid var(--tech-blue);
  border-radius: 8px;
  padding: 8px 12px;
  backdrop-filter: blur(10px);
}

.control-btn {
  background: linear-gradient(45deg, var(--tech-blue), var(--light-blue));
  border: none;
  color: var(--white);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.zoom-info,
.coordinate-info {
  color: var(--white);
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
  min-width: 60px;
  text-align: center;
}

.coordinate-info {
  min-width: 120px;
}

/* 控制面板响应式设计 */
@media (max-width: 1024px) {
  .control-panel {
    top: 15px;
    right: 15px;
  }
}

@media (max-width: 768px) {
  .control-panel {
    top: 10px;
    right: 10px;
    gap: 8px;
  }

  .control-group {
    padding: 6px 10px;
    gap: 8px;
  }

  .control-btn {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .zoom-info,
  .coordinate-info {
    font-size: 0.7rem;
  }

  .coordinate-info {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .control-panel {
    top: 8px;
    right: 8px;
    gap: 6px;
  }

  .control-group {
    padding: 5px 8px;
    gap: 6px;
  }

  .control-btn {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }

  .zoom-info {
    min-width: 45px;
  }

  .coordinate-info {
    min-width: 80px;
    font-size: 0.6rem;
  }
}
</style>
